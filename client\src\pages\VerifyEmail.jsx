import { useEffect, useState, useCallback } from "react";
import { useParams, useNavigate } from "react-router-dom";
import axios from "axios";
import { toast } from "sonner";
import { LoaderCircle, CheckCircle, XCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

const VERIFY_EMAIL_API = "http://localhost:3000/api/auth/verify-email";

const VerifyEmail = () => {
  const { userId, token } = useParams();
  const navigate = useNavigate();
  const [status, setStatus] = useState("verifying"); // 'verifying', 'success', 'failed'

  // Function to verify email
  const verifyEmail = useCallback(async () => {
    if (!userId || !token) {
      setStatus("failed");
      toast.error("Invalid verification link.");
      return;
    }
    setStatus("verifying");
    try {
      const res = await axios.get(`${VERIFY_EMAIL_API}/${userId}/${token}`);
      if (res.data.success) {
        setStatus("success");
        toast.success("Email verified successfully!");
      } else {
        setStatus("failed");
        toast.error(res.data.message || "Verification failed");
      }
    } catch (error) {
      setStatus("failed");
      toast.error(
        error?.response?.data?.message ||
          "Something went wrong while verifying."
      );
    }
  }, [userId, token]);

  useEffect(() => {
    verifyEmail();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [verifyEmail]);

  // Handler for navigating to login
  const handleGoToLogin = () => {
    navigate("/login");
  };

  // Handler for requesting a new verification link (to be implemented)
  const handleResendEmail = async (e) => {
    e?.preventDefault();
    if (!email) {
      toast.error("Email address not found. Please try registering again.");
      navigate("/auth/register");
      return;
    }
    if (!canResend || resendTimer > 0) return;
    setIsResending(true);
    try {
      await axios.post("http://localhost:3000/api/auth/resend-verification", {
        email,
      });
      toast.success("Verification email sent again!");
      setResendTimer(60); // 60 seconds cooldown
      setCanResend(false);
    } catch (error) {
      console.error(error);
      const message =
        error?.response?.data?.message ||
        "Failed to resend verification email. Please try again.";
      toast.error(message);
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background px-4">
      <Card className="w-full max-w-md">
        <CardContent className="py-10 space-y-6 text-center">
          {/* Verifying State */}
          {status === "verifying" && (
            <>
              <div className="flex justify-center">
                <LoaderCircle className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
              <p className="text-sm text-muted-foreground">
                Verifying your email...
              </p>
            </>
          )}

          {/* Success State */}
          {status === "success" && (
            <>
              <div className="flex justify-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <h1 className="text-xl font-semibold text-foreground">
                Email Verified
              </h1>
              <p className="text-muted-foreground text-sm">
                Your account has been successfully verified. You can now log in.
              </p>
              <Button onClick={handleGoToLogin} className="w-full">
                Go to Login
              </Button>
            </>
          )}

          {/* Failed State */}
          {status === "failed" && (
            <>
              <div className="flex justify-center">
                <XCircle className="h-8 w-8 text-red-600" />
              </div>
              <h1 className="text-xl font-semibold text-foreground">
                Verification Failed
              </h1>
              <p className="text-muted-foreground text-sm">
                The verification link is invalid or has expired.
              </p>
              <Button
                variant="outline"
                className="w-full"
                onClick={handleResendEmail}
              >
                Get New Link
              </Button>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default VerifyEmail;
